# EdgeOne UFW Updater

自动获取腾讯云EdgeOne IP列表并更新UFW防火墙规则的工具。

## 功能特性

- 🔄 自动获取最新的EdgeOne IPv4和IPv6地址列表
- 🛡️ 智能比较现有规则，只添加新IP，删除过期IP
- ⏰ 支持定时自动更新（systemd timer）
- 📝 详细的日志记录
- 🔧 灵活的配置选项
- 🚀 一键安装和卸载

## 快速开始

### 1. 下载脚本

```bash
# 克隆或下载项目文件
git clone <repository-url>
cd edgeone
```

### 2. 运行安装程序

```bash
sudo chmod +x install.sh
sudo ./install.sh
```

安装程序会引导您：
- 输入要开放的端口（如：80 443）
- 选择更新频率（每小时/每6小时/每12小时/每天/自定义）
- 自动配置systemd服务和定时器

### 3. 验证安装

```bash
# 查看服务状态
sudo systemctl status edgeone-ufw-updater.timer

# 查看日志
sudo journalctl -u edgeone-ufw-updater

# 手动运行一次
sudo /usr/local/bin/edgeone-ufw-updater.sh
```

## 配置文件

配置文件位于 `/etc/edgeone-ufw/config`：

```bash
# 要开放的端口列表（用空格分隔）
PORTS="80 443"

# EdgeOne API地址
API_URL="https://api.edgeone.ai/ips"

# 请求超时时间（秒）
TIMEOUT=30

# 启用IPv4规则
ENABLE_IPV4=true

# 启用IPv6规则
ENABLE_IPV6=true
```

## 手动使用

### 基本用法

```bash
# 使用默认配置
sudo /usr/local/bin/edgeone-ufw-updater.sh

# 使用自定义配置文件
sudo /usr/local/bin/edgeone-ufw-updater.sh -c /path/to/config

# 详细输出模式
sudo /usr/local/bin/edgeone-ufw-updater.sh -v

# 显示帮助
/usr/local/bin/edgeone-ufw-updater.sh -h
```

### 管理systemd服务

```bash
# 启动定时器
sudo systemctl start edgeone-ufw-updater.timer

# 停止定时器
sudo systemctl stop edgeone-ufw-updater.timer

# 重启定时器
sudo systemctl restart edgeone-ufw-updater.timer

# 查看定时器状态
sudo systemctl status edgeone-ufw-updater.timer

# 查看下次运行时间
sudo systemctl list-timers edgeone-ufw-updater.timer
```

## 日志和监控

### 查看日志

```bash
# 查看systemd日志
sudo journalctl -u edgeone-ufw-updater

# 查看最近的日志
sudo journalctl -u edgeone-ufw-updater -f

# 查看脚本日志文件
sudo tail -f /var/log/edgeone-ufw.log
```

### 监控UFW规则

```bash
# 查看所有UFW规则
sudo ufw status numbered

# 查看EdgeOne相关规则
sudo ufw status numbered | grep EdgeOne

# 查看规则统计
sudo ufw status verbose
```

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   # 确保脚本有执行权限
   sudo chmod +x /usr/local/bin/edgeone-ufw-updater.sh
   ```

2. **网络连接问题**
   ```bash
   # 测试API连接
   curl -s https://api.edgeone.ai/ips | head -5
   ```

3. **UFW未启用**
   ```bash
   # 启用UFW
   sudo ufw enable
   ```

4. **配置文件错误**
   ```bash
   # 检查配置文件语法
   bash -n /etc/edgeone-ufw/config
   ```

### 调试模式

```bash
# 以调试模式运行
sudo bash -x /usr/local/bin/edgeone-ufw-updater.sh -v
```

## 卸载

```bash
# 运行卸载程序
sudo ./install.sh --uninstall
```

或手动卸载：

```bash
# 停止并禁用服务
sudo systemctl stop edgeone-ufw-updater.timer
sudo systemctl disable edgeone-ufw-updater.timer

# 删除文件
sudo rm /etc/systemd/system/edgeone-ufw-updater.service
sudo rm /etc/systemd/system/edgeone-ufw-updater.timer
sudo rm /usr/local/bin/edgeone-ufw-updater.sh
sudo rm -rf /etc/edgeone-ufw
sudo rm -f /var/log/edgeone-ufw.log
sudo rm -rf /var/cache/edgeone-ufw

# 重新加载systemd
sudo systemctl daemon-reload
```

## 安全注意事项

1. **定期检查规则**：建议定期检查UFW规则，确保没有不必要的开放端口
2. **日志监控**：监控日志文件，及时发现异常情况
3. **备份配置**：在修改配置前备份现有的UFW规则
4. **网络安全**：确保只开放必要的端口，避免过度暴露

## 系统要求

- Ubuntu 18.04+ / Debian 9+ / CentOS 7+ 或其他支持systemd的Linux发行版
- UFW防火墙
- curl
- bash 4.0+
- root权限

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 更新日志

### v1.0.0
- 初始版本
- 支持IPv4和IPv6
- 自动安装和配置
- systemd集成
- 智能规则比较和更新
