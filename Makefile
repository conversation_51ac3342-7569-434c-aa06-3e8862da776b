# EdgeOne UFW Updater Makefile
# 用于管理项目的构建、测试和部署

.PHONY: help test install uninstall clean check-syntax package

# 默认目标
help:
	@echo "EdgeOne UFW Updater - 可用命令："
	@echo ""
	@echo "  make test          - 运行所有测试"
	@echo "  make test-api      - 仅测试API连接"
	@echo "  make test-syntax   - 仅测试脚本语法"
	@echo "  make install       - 安装EdgeOne UFW Updater"
	@echo "  make uninstall     - 卸载EdgeOne UFW Updater"
	@echo "  make check-syntax  - 检查所有脚本语法"
	@echo "  make package       - 创建发布包"
	@echo "  make clean         - 清理临时文件"
	@echo "  make help          - 显示此帮助信息"
	@echo ""

# 运行完整测试
test:
	@echo "运行完整测试套件..."
	@chmod +x test.sh 2>/dev/null || true
	@./test.sh

# 仅测试API连接
test-api:
	@echo "测试API连接..."
	@chmod +x test.sh 2>/dev/null || true
	@./test.sh --api-only

# 仅测试脚本语法
test-syntax:
	@echo "测试脚本语法..."
	@chmod +x test.sh 2>/dev/null || true
	@./test.sh --syntax-only

# 安装EdgeOne UFW Updater
install:
	@echo "安装EdgeOne UFW Updater..."
	@if [ "$$(id -u)" -ne 0 ]; then \
		echo "错误: 需要root权限运行安装程序"; \
		echo "请使用: sudo make install"; \
		exit 1; \
	fi
	@chmod +x install.sh 2>/dev/null || true
	@./install.sh

# 卸载EdgeOne UFW Updater
uninstall:
	@echo "卸载EdgeOne UFW Updater..."
	@if [ "$$(id -u)" -ne 0 ]; then \
		echo "错误: 需要root权限运行卸载程序"; \
		echo "请使用: sudo make uninstall"; \
		exit 1; \
	fi
	@chmod +x install.sh 2>/dev/null || true
	@./install.sh --uninstall

# 检查所有脚本语法
check-syntax:
	@echo "检查脚本语法..."
	@echo "检查 edgeone-ufw-updater.sh..."
	@bash -n edgeone-ufw-updater.sh && echo "✓ edgeone-ufw-updater.sh 语法正确"
	@echo "检查 install.sh..."
	@bash -n install.sh && echo "✓ install.sh 语法正确"
	@echo "检查 test.sh..."
	@bash -n test.sh && echo "✓ test.sh 语法正确"
	@echo "检查 config.example..."
	@bash -n config.example && echo "✓ config.example 语法正确"
	@echo "所有脚本语法检查完成"

# 创建发布包
package:
	@echo "创建发布包..."
	@mkdir -p dist
	@tar -czf dist/edgeone-ufw-updater-$$(date +%Y%m%d).tar.gz \
		edgeone-ufw-updater.sh \
		install.sh \
		test.sh \
		config.example \
		README.md \
		Makefile
	@echo "发布包已创建: dist/edgeone-ufw-updater-$$(date +%Y%m%d).tar.gz"

# 清理临时文件
clean:
	@echo "清理临时文件..."
	@rm -rf dist/
	@rm -rf /tmp/edgeone-test/ 2>/dev/null || true
	@echo "清理完成"

# 显示项目状态
status:
	@echo "EdgeOne UFW Updater 项目状态："
	@echo ""
	@echo "文件列表："
	@ls -la *.sh *.md Makefile config.example 2>/dev/null || true
	@echo ""
	@if command -v systemctl >/dev/null 2>&1; then \
		echo "系统服务状态："; \
		systemctl is-active edgeone-ufw-updater.timer 2>/dev/null || echo "服务未安装"; \
	fi

# 开发者工具
dev-setup:
	@echo "设置开发环境..."
	@chmod +x *.sh 2>/dev/null || true
	@echo "开发环境设置完成"

# 快速部署（测试环境）
dev-deploy:
	@echo "快速部署到测试环境..."
	@make check-syntax
	@make test-api
	@echo "测试通过，可以进行安装"

# 显示使用统计
stats:
	@echo "项目统计信息："
	@echo "脚本文件数量: $$(ls -1 *.sh | wc -l)"
	@echo "总代码行数: $$(cat *.sh *.md Makefile config.example | wc -l)"
	@echo "主脚本行数: $$(wc -l < edgeone-ufw-updater.sh)"
	@echo "安装脚本行数: $$(wc -l < install.sh)"
	@echo "测试脚本行数: $$(wc -l < test.sh)"
