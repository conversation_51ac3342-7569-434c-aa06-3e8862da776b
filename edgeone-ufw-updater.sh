#!/bin/bash

# EdgeOne UFW Updater Script
# 自动获取腾讯云EdgeOne IP列表并更新UFW防火墙规则

set -euo pipefail

# 配置文件路径
CONFIG_FILE="/etc/edgeone-ufw/config"
CACHE_DIR="/var/cache/edgeone-ufw"
LOG_FILE="/var/log/edgeone-ufw.log"
CURRENT_IPS_FILE="$CACHE_DIR/current_ips.txt"
NEW_IPS_FILE="$CACHE_DIR/new_ips.txt"
RULE_PREFIX="EdgeOne"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理
error_exit() {
    log "ERROR: $1"
    exit 1
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error_exit "此脚本需要root权限运行"
    fi
}

# 创建必要的目录
setup_directories() {
    mkdir -p "$CACHE_DIR"
    mkdir -p "$(dirname "$CONFIG_FILE")"
    mkdir -p "$(dirname "$LOG_FILE")"
}

# 读取配置
load_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        error_exit "配置文件不存在: $CONFIG_FILE"
    fi
    
    source "$CONFIG_FILE"
    
    if [[ -z "${PORTS:-}" ]]; then
        error_exit "配置文件中未设置PORTS变量"
    fi
    
    # 设置默认值
    API_URL="${API_URL:-https://api.edgeone.ai/ips}"
    TIMEOUT="${TIMEOUT:-30}"
    ENABLE_IPV4="${ENABLE_IPV4:-true}"
    ENABLE_IPV6="${ENABLE_IPV6:-true}"
}

# 获取EdgeOne IP列表
fetch_ips() {
    log "正在获取EdgeOne IP列表..."
    
    if ! curl -s --connect-timeout "$TIMEOUT" --max-time "$TIMEOUT" "$API_URL" > "$NEW_IPS_FILE"; then
        error_exit "无法获取IP列表"
    fi
    
    # 验证文件不为空
    if [[ ! -s "$NEW_IPS_FILE" ]]; then
        error_exit "获取的IP列表为空"
    fi
    
    log "成功获取IP列表，共 $(wc -l < "$NEW_IPS_FILE") 条记录"
}

# 过滤IP地址
filter_ips() {
    local temp_file=$(mktemp)
    
    while IFS= read -r line; do
        # 跳过空行和注释
        [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]] && continue
        
        # 检查是否为有效的IPv4或IPv6 CIDR
        if [[ "$ENABLE_IPV4" == "true" && "$line" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/[0-9]+$ ]]; then
            echo "$line" >> "$temp_file"
        elif [[ "$ENABLE_IPV6" == "true" && "$line" =~ ^[0-9a-fA-F:]+/[0-9]+$ ]]; then
            echo "$line" >> "$temp_file"
        fi
    done < "$NEW_IPS_FILE"
    
    mv "$temp_file" "$NEW_IPS_FILE"
    log "过滤后的IP列表，共 $(wc -l < "$NEW_IPS_FILE") 条记录"
}

# 获取当前UFW中的EdgeOne规则
get_current_rules() {
    ufw status numbered | grep "$RULE_PREFIX" | awk '{print $NF}' | sort > "$CURRENT_IPS_FILE" 2>/dev/null || touch "$CURRENT_IPS_FILE"
}

# 比较IP列表并返回需要添加和删除的IP
compare_ips() {
    local add_file=$(mktemp)
    local remove_file=$(mktemp)
    
    # 排序新IP列表
    sort "$NEW_IPS_FILE" -o "$NEW_IPS_FILE"
    
    # 找出需要添加的IP（在新列表中但不在当前规则中）
    comm -23 "$NEW_IPS_FILE" "$CURRENT_IPS_FILE" > "$add_file"
    
    # 找出需要删除的IP（在当前规则中但不在新列表中）
    comm -13 "$NEW_IPS_FILE" "$CURRENT_IPS_FILE" > "$remove_file"
    
    echo "$add_file $remove_file"
}

# 添加UFW规则
add_rules() {
    local ip_file="$1"
    local count=0
    
    while IFS= read -r ip; do
        [[ -z "$ip" ]] && continue
        
        for port in $PORTS; do
            if ufw allow from "$ip" to any port "$port" comment "$RULE_PREFIX-$(date +%Y%m%d)" >/dev/null 2>&1; then
                ((count++))
                log "添加规则: $ip -> 端口 $port"
            else
                log "WARNING: 无法添加规则: $ip -> 端口 $port"
            fi
        done
    done < "$ip_file"
    
    log "成功添加 $count 条规则"
}

# 删除UFW规则
remove_rules() {
    local ip_file="$1"
    local count=0
    
    while IFS= read -r ip; do
        [[ -z "$ip" ]] && continue
        
        # 获取包含该IP的规则编号（倒序删除避免编号变化）
        local rule_numbers=$(ufw status numbered | grep "$ip" | grep "$RULE_PREFIX" | awk '{print $1}' | tr -d '[]' | sort -nr)
        
        for rule_num in $rule_numbers; do
            if echo "y" | ufw delete "$rule_num" >/dev/null 2>&1; then
                ((count++))
                log "删除规则: $ip (规则编号: $rule_num)"
            else
                log "WARNING: 无法删除规则编号: $rule_num"
            fi
        done
    done < "$ip_file"
    
    log "成功删除 $count 条规则"
}

# 主函数
main() {
    log "开始执行EdgeOne UFW更新任务"

    check_root
    setup_directories
    load_config

    # 如果指定了清理端口规则选项
    if [[ "${CLEANUP_PORTS:-false}" == "true" ]]; then
        cleanup_port_rules
    fi

    # 如果指定了强制清理选项
    if [[ "${FORCE_CLEANUP:-false}" == "true" ]]; then
        log "强制清理所有EdgeOne规则..."
        # 删除所有包含EdgeOne标识的规则
        local edgeone_rules=$(ufw status numbered | grep "$RULE_PREFIX" | awk '{print $1}' | tr -d '[]' | sort -nr)
        local cleanup_count=0
        for rule_num in $edgeone_rules; do
            if [[ -n "$rule_num" ]]; then
                if echo "y" | ufw delete "$rule_num" >/dev/null 2>&1; then
                    ((cleanup_count++))
                    log "删除EdgeOne规则 #$rule_num"
                fi
            fi
        done
        log "强制清理完成，删除了 $cleanup_count 条EdgeOne规则"
        # 清空当前规则缓存，强制重新创建所有规则
        > "$CURRENT_IPS_FILE"
    fi

    # 获取并过滤IP列表
    fetch_ips
    filter_ips

    # 获取当前规则
    get_current_rules

    # 比较IP列表
    local files=($(compare_ips))
    local add_file="${files[0]}"
    local remove_file="${files[1]}"

    local add_count=$(wc -l < "$add_file")
    local remove_count=$(wc -l < "$remove_file")

    log "需要添加 $add_count 个IP，删除 $remove_count 个IP"

    # 删除旧规则
    if [[ $remove_count -gt 0 ]]; then
        log "开始删除过期规则..."
        remove_rules "$remove_file"
    fi

    # 添加新规则
    if [[ $add_count -gt 0 ]]; then
        log "开始添加新规则..."
        add_rules "$add_file"
    fi

    # 更新缓存
    cp "$NEW_IPS_FILE" "$CURRENT_IPS_FILE"

    # 清理临时文件
    rm -f "$add_file" "$remove_file"

    log "EdgeOne UFW更新任务完成"
}

# 清理指定端口的所有现有规则
cleanup_port_rules() {
    log "开始清理指定端口的所有现有规则..."

    local total_removed=0

    for port in $PORTS; do
        log "清理端口 $port 的所有规则..."

        # 获取该端口的所有规则编号（倒序删除避免编号变化）
        local rule_numbers=$(ufw status numbered | grep "ALLOW.*$port" | awk '{print $1}' | tr -d '[]' | sort -nr)

        local port_removed=0
        for rule_num in $rule_numbers; do
            if [[ -n "$rule_num" ]]; then
                log "删除规则 #$rule_num (端口 $port)"
                if echo "y" | ufw delete "$rule_num" >/dev/null 2>&1; then
                    ((port_removed++))
                    ((total_removed++))
                else
                    log "WARNING: 无法删除规则 #$rule_num"
                fi
            fi
        done

        log "端口 $port: 删除了 $port_removed 条规则"
    done

    log "总共删除了 $total_removed 条现有规则"
}

# 显示帮助信息
show_help() {
    cat << EOF
EdgeOne UFW Updater - 腾讯云EdgeOne IP列表自动更新工具

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -c, --config        指定配置文件路径 (默认: $CONFIG_FILE)
    -v, --verbose       详细输出模式
    --cleanup-ports     清理指定端口的所有现有规则后再更新
    --force-cleanup     强制清理所有EdgeOne规则后重新创建

配置文件格式:
    PORTS="80 443"              # 要开放的端口列表
    API_URL="https://api.edgeone.ai/ips"  # EdgeOne API地址
    TIMEOUT=30                  # 请求超时时间（秒）
    ENABLE_IPV4=true           # 启用IPv4规则
    ENABLE_IPV6=true           # 启用IPv6规则

示例:
    $0                         # 使用默认配置运行
    $0 -c /path/to/config      # 使用指定配置文件
    $0 -v                      # 详细输出模式
    $0 --cleanup-ports         # 清理端口规则后更新
    $0 --force-cleanup         # 强制重新创建所有规则

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        *)
            error_exit "未知参数: $1"
            ;;
    esac
done

# 执行主函数
main
