#!/bin/bash

# EdgeOne UFW Updater 安装脚本
# 自动安装和配置EdgeOne UFW更新工具

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_NAME="edgeone-ufw-updater.sh"
INSTALL_DIR="/usr/local/bin"
CONFIG_DIR="/etc/edgeone-ufw"
CONFIG_FILE="$CONFIG_DIR/config"
SERVICE_NAME="edgeone-ufw-updater"
TIMER_NAME="edgeone-ufw-updater.timer"

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 错误处理
error_exit() {
    print_error "$1"
    exit 1
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error_exit "此安装脚本需要root权限运行"
    fi
}

# 检查系统要求
check_requirements() {
    print_info "检查系统要求..."
    
    # 检查操作系统
    if ! command -v ufw &> /dev/null; then
        error_exit "UFW防火墙未安装，请先安装UFW"
    fi
    
    if ! command -v curl &> /dev/null; then
        error_exit "curl未安装，请先安装curl"
    fi
    
    if ! command -v systemctl &> /dev/null; then
        error_exit "systemd未安装，此脚本需要systemd支持"
    fi
    
    print_success "系统要求检查通过"
}

# 获取用户输入的端口
get_ports() {
    echo
    print_info "请输入要为EdgeOne IP开放的端口（用空格分隔，例如: 80 443 8080）:"
    read -r PORTS

    if [[ -z "$PORTS" ]]; then
        error_exit "端口不能为空"
    fi

    # 验证端口格式
    for port in $PORTS; do
        if ! [[ "$port" =~ ^[0-9]+$ ]] || [[ "$port" -lt 1 || "$port" -gt 65535 ]]; then
            error_exit "无效的端口号: $port"
        fi
    done

    print_success "端口设置: $PORTS"
}

# 询问是否清理现有规则
ask_cleanup_existing_rules() {
    echo
    print_info "检查指定端口的现有UFW规则..."

    # 首先显示当前UFW状态以便调试
    print_info "当前UFW状态："
    ufw status numbered | head -10
    echo

    local has_existing_rules=false
    for port in $PORTS; do
        # 检测包含指定端口的规则，根据UFW实际输出格式进行匹配
        # UFW格式: "[ 3] 44344                      ALLOW IN    **********/23"
        # 匹配端口号在第二列的情况
        local existing_rules=$(ufw status numbered | grep -E "\[[[:space:]]*[0-9]+\][[:space:]]+${port}([[:space:]]+|/)" | head -10)

        if [[ -n "$existing_rules" ]]; then
            has_existing_rules=true
            print_warning "端口 $port 的现有规则："
            echo "$existing_rules" | while read -r line; do
                echo "  $line"
            done
            echo
        else
            print_info "端口 $port: 未发现现有规则"
        fi
    done

    if [[ "$has_existing_rules" == "true" ]]; then
        echo
        print_warning "发现指定端口的现有规则！"
        print_info "是否要删除这些端口的所有现有规则？"
        echo "1) 是 - 删除所有现有规则，从干净状态开始"
        echo "2) 否 - 保留现有规则，只添加EdgeOne规则"
        echo
        read -p "请选择 (1-2): " cleanup_choice

        case $cleanup_choice in
            1)
                CLEANUP_EXISTING_RULES=true
                print_warning "将删除指定端口的所有现有规则"
                ;;
            2)
                CLEANUP_EXISTING_RULES=false
                print_info "将保留现有规则"
                ;;
            *)
                print_warning "无效选择，默认保留现有规则"
                CLEANUP_EXISTING_RULES=false
                ;;
        esac
    else
        print_info "未发现指定端口的现有规则，将直接添加EdgeOne规则"
        CLEANUP_EXISTING_RULES=false
    fi
}

# 清理现有规则
cleanup_existing_rules() {
    if [[ "$CLEANUP_EXISTING_RULES" != "true" ]]; then
        return 0
    fi

    print_info "开始清理现有规则..."

    local total_removed=0

    for port in $PORTS; do
        print_info "清理端口 $port 的规则..."

        # 使用与检测逻辑相同的匹配方式
        # 匹配UFW格式: "[ 3] 44344                      ALLOW IN    **********/23"
        local rule_numbers=$(ufw status numbered | grep -E "\[[[:space:]]*[0-9]+\][[:space:]]+${port}([[:space:]]+|/)" | awk '{print $1}' | tr -d '[]' | sort -nr)

        local port_removed=0
        for rule_num in $rule_numbers; do
            if [[ -n "$rule_num" && "$rule_num" =~ ^[0-9]+$ ]]; then
                # 获取规则详情用于日志
                local rule_detail=$(ufw status numbered | grep "^\[${rule_num}\]" | head -1)
                print_info "删除规则 #$rule_num: $rule_detail"

                if echo "y" | ufw delete "$rule_num" >/dev/null 2>&1; then
                    ((port_removed++))
                    ((total_removed++))
                    print_success "成功删除规则 #$rule_num"
                else
                    print_warning "无法删除规则 #$rule_num"
                fi

                # 删除规则后稍作延迟，避免编号混乱
                sleep 0.1
            fi
        done

        if [[ $port_removed -gt 0 ]]; then
            print_success "端口 $port: 删除了 $port_removed 条规则"
        else
            print_info "端口 $port: 没有需要删除的规则"
        fi
    done

    if [[ $total_removed -gt 0 ]]; then
        print_success "总共删除了 $total_removed 条现有规则"

        # 显示清理后的状态
        echo
        print_info "清理后的UFW状态："
        ufw status numbered | head -20

        # 如果规则很多，显示总数
        local total_rules=$(ufw status numbered | grep -c "^\[")
        if [[ $total_rules -gt 20 ]]; then
            print_info "... (总共 $total_rules 条规则)"
        fi
    else
        print_info "没有删除任何规则"
    fi
}

# 获取更新频率
get_update_frequency() {
    echo
    print_info "请选择更新频率:"
    echo "1) 每小时"
    echo "2) 每6小时"
    echo "3) 每12小时"
    echo "4) 每天"
    echo "5) 自定义"
    
    read -p "请选择 (1-5): " choice
    
    case $choice in
        1)
            TIMER_SCHEDULE="hourly"
            TIMER_ONCALENDAR="*:0:0"
            ;;
        2)
            TIMER_SCHEDULE="每6小时"
            TIMER_ONCALENDAR="*-*-* 0/6:0:0"
            ;;
        3)
            TIMER_SCHEDULE="每12小时"
            TIMER_ONCALENDAR="*-*-* 0/12:0:0"
            ;;
        4)
            TIMER_SCHEDULE="每天"
            TIMER_ONCALENDAR="daily"
            ;;
        5)
            print_info "请输入systemd timer格式的时间表达式 (例如: *-*-* 2:0:0 表示每天凌晨2点):"
            read -r TIMER_ONCALENDAR
            TIMER_SCHEDULE="自定义"
            ;;
        *)
            error_exit "无效的选择"
            ;;
    esac
    
    print_success "更新频率设置: $TIMER_SCHEDULE"
}

# 安装主脚本
install_script() {
    print_info "安装主脚本..."
    
    if [[ ! -f "$SCRIPT_NAME" ]]; then
        error_exit "找不到主脚本文件: $SCRIPT_NAME"
    fi
    
    cp "$SCRIPT_NAME" "$INSTALL_DIR/"
    chmod +x "$INSTALL_DIR/$SCRIPT_NAME"
    
    print_success "主脚本已安装到: $INSTALL_DIR/$SCRIPT_NAME"
}

# 创建配置文件
create_config() {
    print_info "创建配置文件..."
    
    mkdir -p "$CONFIG_DIR"
    
    cat > "$CONFIG_FILE" << EOF
# EdgeOne UFW Updater 配置文件

# 要开放的端口列表（用空格分隔）
PORTS="$PORTS"

# EdgeOne API地址
API_URL="https://api.edgeone.ai/ips"

# 请求超时时间（秒）
TIMEOUT=30

# 启用IPv4规则
ENABLE_IPV4=true

# 启用IPv6规则
ENABLE_IPV6=true
EOF
    
    chmod 600 "$CONFIG_FILE"
    print_success "配置文件已创建: $CONFIG_FILE"
}

# 创建systemd服务
create_systemd_service() {
    print_info "创建systemd服务..."
    
    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=EdgeOne UFW Updater
After=network.target

[Service]
Type=oneshot
ExecStart=$INSTALL_DIR/$SCRIPT_NAME
User=root
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    print_success "systemd服务已创建"
}

# 创建systemd定时器
create_systemd_timer() {
    print_info "创建systemd定时器..."
    
    cat > "/etc/systemd/system/$TIMER_NAME" << EOF
[Unit]
Description=EdgeOne UFW Updater Timer
Requires=$SERVICE_NAME.service

[Timer]
OnCalendar=$TIMER_ONCALENDAR
Persistent=true

[Install]
WantedBy=timers.target
EOF
    
    print_success "systemd定时器已创建"
}

# 启用并启动服务
enable_services() {
    print_info "启用并启动服务..."
    
    systemctl daemon-reload
    systemctl enable "$TIMER_NAME"
    systemctl start "$TIMER_NAME"
    
    print_success "服务已启用并启动"
}

# 运行初始更新
run_initial_update() {
    print_info "运行初始更新..."
    
    if "$INSTALL_DIR/$SCRIPT_NAME"; then
        print_success "初始更新完成"
    else
        print_warning "初始更新失败，请检查配置"
    fi
}

# 显示安装后信息
show_post_install_info() {
    echo
    print_success "EdgeOne UFW Updater 安装完成！"
    echo
    echo "安装信息:"
    echo "  主脚本: $INSTALL_DIR/$SCRIPT_NAME"
    echo "  配置文件: $CONFIG_FILE"
    echo "  更新频率: $TIMER_SCHEDULE"
    echo "  开放端口: $PORTS"
    echo
    echo "常用命令:"
    echo "  查看服务状态: systemctl status $TIMER_NAME"
    echo "  查看日志: journalctl -u $SERVICE_NAME"
    echo "  手动运行: $INSTALL_DIR/$SCRIPT_NAME"
    echo "  编辑配置: nano $CONFIG_FILE"
    echo "  重启定时器: systemctl restart $TIMER_NAME"
    echo
    echo "卸载命令:"
    echo "  systemctl stop $TIMER_NAME"
    echo "  systemctl disable $TIMER_NAME"
    echo "  rm /etc/systemd/system/$SERVICE_NAME.service"
    echo "  rm /etc/systemd/system/$TIMER_NAME"
    echo "  rm $INSTALL_DIR/$SCRIPT_NAME"
    echo "  rm -rf $CONFIG_DIR"
    echo
}

# 主安装函数
main() {
    echo "========================================"
    echo "EdgeOne UFW Updater 安装程序"
    echo "========================================"
    echo

    check_root
    check_requirements
    get_ports
    ask_cleanup_existing_rules
    get_update_frequency

    echo
    print_info "开始安装..."

    # 如果选择清理现有规则，先执行清理
    cleanup_existing_rules

    install_script
    create_config
    create_systemd_service
    create_systemd_timer
    enable_services
    run_initial_update

    show_post_install_info
}

# 显示帮助信息
show_help() {
    cat << EOF
EdgeOne UFW Updater 安装程序

用法: $0 [选项]

选项:
    -h, --help      显示此帮助信息
    --uninstall     卸载EdgeOne UFW Updater

此安装程序将:
1. 安装主脚本到 $INSTALL_DIR
2. 创建配置文件到 $CONFIG_DIR
3. 设置systemd服务和定时器
4. 运行初始IP列表更新

EOF
}

# 卸载函数
uninstall() {
    print_info "开始卸载EdgeOne UFW Updater..."
    
    # 停止并禁用服务
    systemctl stop "$TIMER_NAME" 2>/dev/null || true
    systemctl disable "$TIMER_NAME" 2>/dev/null || true
    
    # 删除文件
    rm -f "/etc/systemd/system/$SERVICE_NAME.service"
    rm -f "/etc/systemd/system/$TIMER_NAME"
    rm -f "$INSTALL_DIR/$SCRIPT_NAME"
    
    # 询问是否删除配置和日志
    read -p "是否删除配置文件和日志? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf "$CONFIG_DIR"
        rm -f "/var/log/edgeone-ufw.log"
        rm -rf "/var/cache/edgeone-ufw"
        print_success "配置文件和日志已删除"
    fi
    
    systemctl daemon-reload
    
    print_success "EdgeOne UFW Updater 已卸载"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --uninstall)
            check_root
            uninstall
            exit 0
            ;;
        *)
            error_exit "未知参数: $1"
            ;;
    esac
done

# 执行主函数
main
