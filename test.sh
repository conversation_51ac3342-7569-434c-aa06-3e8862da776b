#!/bin/bash

# EdgeOne UFW Updater 测试脚本
# 用于测试API连接和脚本功能

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试配置
API_URL="https://api.edgeone.ai/ips"
TIMEOUT=30
TEST_DIR="/tmp/edgeone-test"

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试API连接
test_api_connection() {
    print_info "测试EdgeOne API连接..."
    
    local temp_file="$TEST_DIR/api_test.txt"
    
    if curl -s --connect-timeout "$TIMEOUT" --max-time "$TIMEOUT" "$API_URL" > "$temp_file"; then
        local line_count=$(wc -l < "$temp_file")
        if [[ $line_count -gt 0 ]]; then
            print_success "API连接成功，获取到 $line_count 条IP记录"
            
            # 显示前5条记录作为示例
            print_info "前5条IP记录："
            head -5 "$temp_file" | while read -r line; do
                echo "  $line"
            done
        else
            print_error "API返回空数据"
            return 1
        fi
    else
        print_error "无法连接到EdgeOne API"
        return 1
    fi
}

# 测试IP格式验证
test_ip_validation() {
    print_info "测试IP格式验证..."
    
    local temp_file="$TEST_DIR/api_test.txt"
    local ipv4_count=0
    local ipv6_count=0
    local invalid_count=0
    
    while IFS= read -r line; do
        [[ -z "$line" ]] && continue
        
        if [[ "$line" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/[0-9]+$ ]]; then
            ((ipv4_count++))
        elif [[ "$line" =~ ^[0-9a-fA-F:]+/[0-9]+$ ]]; then
            ((ipv6_count++))
        else
            ((invalid_count++))
            print_warning "无效的IP格式: $line"
        fi
    done < "$temp_file"
    
    print_success "IPv4地址: $ipv4_count 条"
    print_success "IPv6地址: $ipv6_count 条"
    
    if [[ $invalid_count -gt 0 ]]; then
        print_warning "无效格式: $invalid_count 条"
    else
        print_success "所有IP格式验证通过"
    fi
}

# 测试系统要求
test_system_requirements() {
    print_info "检查系统要求..."
    
    local missing_tools=()
    
    # 检查必需的工具
    for tool in curl ufw systemctl; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -eq 0 ]]; then
        print_success "所有必需工具已安装"
    else
        print_error "缺少以下工具: ${missing_tools[*]}"
        return 1
    fi
    
    # 检查UFW状态
    if ufw status &> /dev/null; then
        local ufw_status=$(ufw status | head -1)
        print_info "UFW状态: $ufw_status"
    else
        print_warning "无法获取UFW状态（可能需要root权限）"
    fi
}

# 测试配置文件
test_config_file() {
    print_info "测试配置文件格式..."
    
    local config_file="config.example"
    
    if [[ -f "$config_file" ]]; then
        if bash -n "$config_file"; then
            print_success "配置文件语法正确"
            
            # 加载配置并显示
            source "$config_file"
            print_info "配置内容："
            echo "  端口: ${PORTS:-未设置}"
            echo "  API地址: ${API_URL:-未设置}"
            echo "  超时时间: ${TIMEOUT:-未设置}秒"
            echo "  启用IPv4: ${ENABLE_IPV4:-未设置}"
            echo "  启用IPv6: ${ENABLE_IPV6:-未设置}"
        else
            print_error "配置文件语法错误"
            return 1
        fi
    else
        print_warning "配置文件不存在: $config_file"
    fi
}

# 测试主脚本语法
test_main_script() {
    print_info "测试主脚本语法..."
    
    local script_file="edgeone-ufw-updater.sh"
    
    if [[ -f "$script_file" ]]; then
        if bash -n "$script_file"; then
            print_success "主脚本语法正确"
        else
            print_error "主脚本语法错误"
            return 1
        fi
    else
        print_error "主脚本不存在: $script_file"
        return 1
    fi
}

# 测试安装脚本语法
test_install_script() {
    print_info "测试安装脚本语法..."
    
    local install_file="install.sh"
    
    if [[ -f "$install_file" ]]; then
        if bash -n "$install_file"; then
            print_success "安装脚本语法正确"
        else
            print_error "安装脚本语法错误"
            return 1
        fi
    else
        print_error "安装脚本不存在: $install_file"
        return 1
    fi
}

# 性能测试
test_performance() {
    print_info "测试API响应性能..."
    
    local start_time=$(date +%s.%N)
    
    if curl -s --connect-timeout "$TIMEOUT" --max-time "$TIMEOUT" "$API_URL" > /dev/null; then
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "N/A")
        
        if [[ "$duration" != "N/A" ]]; then
            print_success "API响应时间: ${duration}秒"
        else
            print_success "API响应成功"
        fi
    else
        print_error "API响应测试失败"
        return 1
    fi
}

# 清理测试文件
cleanup() {
    rm -rf "$TEST_DIR"
}

# 主测试函数
main() {
    echo "========================================"
    echo "EdgeOne UFW Updater 测试程序"
    echo "========================================"
    echo
    
    # 创建测试目录
    mkdir -p "$TEST_DIR"
    
    local failed_tests=0
    
    # 运行所有测试
    test_system_requirements || ((failed_tests++))
    echo
    
    test_api_connection || ((failed_tests++))
    echo
    
    test_ip_validation || ((failed_tests++))
    echo
    
    test_config_file || ((failed_tests++))
    echo
    
    test_main_script || ((failed_tests++))
    echo
    
    test_install_script || ((failed_tests++))
    echo
    
    test_performance || ((failed_tests++))
    echo
    
    # 清理
    cleanup
    
    # 显示测试结果
    echo "========================================"
    if [[ $failed_tests -eq 0 ]]; then
        print_success "所有测试通过！"
        echo
        print_info "您可以运行以下命令开始安装："
        echo "  sudo ./install.sh"
    else
        print_error "有 $failed_tests 个测试失败"
        echo
        print_info "请修复上述问题后重新运行测试"
    fi
    echo "========================================"
    
    return $failed_tests
}

# 显示帮助信息
show_help() {
    cat << EOF
EdgeOne UFW Updater 测试程序

用法: $0 [选项]

选项:
    -h, --help      显示此帮助信息
    --api-only      仅测试API连接
    --syntax-only   仅测试脚本语法

此测试程序将检查：
1. 系统要求（curl, ufw, systemctl等）
2. EdgeOne API连接和数据格式
3. 配置文件格式
4. 脚本语法正确性
5. API响应性能

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --api-only)
            mkdir -p "$TEST_DIR"
            test_api_connection
            test_ip_validation
            cleanup
            exit $?
            ;;
        --syntax-only)
            test_config_file
            test_main_script
            test_install_script
            exit $?
            ;;
        *)
            print_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 执行主函数
main
