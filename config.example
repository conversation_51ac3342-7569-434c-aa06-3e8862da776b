# EdgeOne UFW Updater 配置文件示例
# 复制此文件到 /etc/edgeone-ufw/config 并根据需要修改

# 要开放的端口列表（用空格分隔）
# 示例：Web服务器通常需要80和443端口
PORTS="80 443"

# EdgeOne API地址
# 默认使用官方API地址，通常不需要修改
API_URL="https://api.edgeone.ai/ips"

# 请求超时时间（秒）
# 如果网络较慢，可以适当增加此值
TIMEOUT=30

# 启用IPv4规则
# 设置为false可以禁用IPv4规则的处理
ENABLE_IPV4=true

# 启用IPv6规则
# 设置为false可以禁用IPv6规则的处理
ENABLE_IPV6=true

# 高级配置选项（可选）

# 自定义规则注释前缀
# 用于标识由此脚本创建的规则
# RULE_PREFIX="EdgeOne"

# 缓存目录
# 用于存储临时文件和IP列表缓存
# CACHE_DIR="/var/cache/edgeone-ufw"

# 日志文件路径
# LOG_FILE="/var/log/edgeone-ufw.log"

# 配置示例说明：

# 1. 仅开放HTTP端口：
# PORTS="80"

# 2. 开放多个端口（Web + SSH + 自定义）：
# PORTS="22 80 443 8080 8443"

# 3. 仅处理IPv4地址：
# ENABLE_IPV4=true
# ENABLE_IPV6=false

# 4. 仅处理IPv6地址：
# ENABLE_IPV4=false
# ENABLE_IPV6=true

# 5. 增加超时时间（适用于网络较慢的环境）：
# TIMEOUT=60
