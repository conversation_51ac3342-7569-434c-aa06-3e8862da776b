#!/bin/bash

# 测试UFW规则匹配的正则表达式

# 模拟UFW输出
cat << 'EOF' > /tmp/test_ufw_output.txt
Status: active

     To                         Action      From
     --                         ------      ----
[ 1] 22/tcp                     ALLOW IN    Anywhere                  
[ 2] 4567                       ALLOW IN    Anywhere                  
[ 3] 44344                      ALLOW IN    1.71.146.0/23             
[ 4] 44344                      ALLOW IN    101.33.0.0/19             
[ 5] 44344                      ALLOW IN    101.33.195.0/24           
[ 6] 44344                      ALLOW IN    101.71.100.0/23           
[ 7] 80/tcp                     ALLOW IN    Anywhere                  
[ 8] 443/tcp                    ALLOW IN    Anywhere                  
EOF

echo "测试UFW输出："
cat /tmp/test_ufw_output.txt
echo
echo "=========================================="

# 测试端口44344的匹配
port=44344
echo "测试端口 $port 的规则检测："

echo "新的正则表达式匹配结果："
grep -E "\[[[:space:]]*[0-9]+\][[:space:]]+${port}([[:space:]]+|/)" /tmp/test_ufw_output.txt

echo
echo "提取规则编号："
grep -E "\[[[:space:]]*[0-9]+\][[:space:]]+${port}([[:space:]]+|/)" /tmp/test_ufw_output.txt | awk '{print $1}' | tr -d '[]'

# 清理
rm -f /tmp/test_ufw_output.txt
