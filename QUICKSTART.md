# EdgeOne UFW Updater 快速开始指南

## 🚀 一分钟快速部署

### 1. 下载并测试

```bash
# 下载项目文件
git clone <your-repo-url>
cd edgeone

# 运行测试确保环境正常
./test.sh
```

### 2. 一键安装

```bash
# 运行安装程序（需要root权限）
sudo ./install.sh
```

安装程序会询问：
- **端口设置**：输入要开放的端口，如 `80 443`
- **更新频率**：选择自动更新的时间间隔

### 3. 验证安装

```bash
# 查看服务状态
sudo systemctl status edgeone-ufw-updater.timer

# 查看UFW规则
sudo ufw status numbered | grep EdgeOne
```

## 📋 详细安装步骤

### 前置要求

确保系统已安装：
- UFW防火墙：`sudo apt install ufw`
- curl：`sudo apt install curl`
- systemd（大多数现代Linux发行版已包含）

### 安装过程

1. **下载脚本**
   ```bash
   wget https://raw.githubusercontent.com/your-repo/edgeone-ufw-updater/main/install.sh
   wget https://raw.githubusercontent.com/your-repo/edgeone-ufw-updater/main/edgeone-ufw-updater.sh
   chmod +x *.sh
   ```

2. **运行安装**
   ```bash
   sudo ./install.sh
   ```

3. **配置选项**
   - 端口：输入需要为EdgeOne开放的端口（空格分隔）
   - 更新频率：选择1-5中的一个选项

4. **完成安装**
   - 脚本会自动配置systemd服务
   - 运行初始IP列表更新
   - 显示管理命令

## 🔧 常用管理命令

```bash
# 查看服务状态
sudo systemctl status edgeone-ufw-updater.timer

# 手动运行更新
sudo /usr/local/bin/edgeone-ufw-updater.sh

# 查看日志
sudo journalctl -u edgeone-ufw-updater -f

# 查看UFW规则
sudo ufw status numbered

# 编辑配置
sudo nano /etc/edgeone-ufw/config

# 重启服务
sudo systemctl restart edgeone-ufw-updater.timer
```

## 📊 监控和维护

### 检查更新状态
```bash
# 查看最近的更新日志
sudo tail -20 /var/log/edgeone-ufw.log

# 查看定时器下次运行时间
sudo systemctl list-timers edgeone-ufw-updater.timer
```

### 验证规则有效性
```bash
# 统计EdgeOne规则数量
sudo ufw status numbered | grep EdgeOne | wc -l

# 查看最新的IP列表
curl -s https://api.edgeone.ai/ips | wc -l
```

## 🛠️ 故障排除

### 常见问题

**问题1：权限被拒绝**
```bash
# 解决方案：确保使用sudo运行
sudo ./install.sh
```

**问题2：无法连接API**
```bash
# 测试网络连接
curl -s https://api.edgeone.ai/ips | head -5

# 检查防火墙设置
sudo ufw status
```

**问题3：服务未启动**
```bash
# 检查服务状态
sudo systemctl status edgeone-ufw-updater.timer

# 重新启动服务
sudo systemctl restart edgeone-ufw-updater.timer
```

**问题4：规则未更新**
```bash
# 手动运行更新
sudo /usr/local/bin/edgeone-ufw-updater.sh -v

# 检查配置文件
sudo cat /etc/edgeone-ufw/config
```

## 🔄 卸载

### 自动卸载
```bash
sudo ./install.sh --uninstall
```

### 手动卸载
```bash
# 停止服务
sudo systemctl stop edgeone-ufw-updater.timer
sudo systemctl disable edgeone-ufw-updater.timer

# 删除文件
sudo rm /etc/systemd/system/edgeone-ufw-updater.*
sudo rm /usr/local/bin/edgeone-ufw-updater.sh
sudo rm -rf /etc/edgeone-ufw

# 重新加载systemd
sudo systemctl daemon-reload
```

## 📈 高级配置

### 自定义更新频率
编辑 `/etc/systemd/system/edgeone-ufw-updater.timer`：
```ini
[Timer]
OnCalendar=*-*-* 2:0:0  # 每天凌晨2点
# OnCalendar=*:0/30:0   # 每30分钟
# OnCalendar=Mon *-*-* 1:0:0  # 每周一凌晨1点
```

### 仅处理IPv4或IPv6
编辑 `/etc/edgeone-ufw/config`：
```bash
# 仅IPv4
ENABLE_IPV4=true
ENABLE_IPV6=false

# 仅IPv6
ENABLE_IPV4=false
ENABLE_IPV6=true
```

### 增加端口
编辑 `/etc/edgeone-ufw/config`：
```bash
PORTS="22 80 443 8080 8443"
```

然后重启服务：
```bash
sudo systemctl restart edgeone-ufw-updater.timer
```

## 📞 获取帮助

- 查看详细文档：`cat README.md`
- 运行测试：`./test.sh`
- 查看脚本帮助：`./edgeone-ufw-updater.sh -h`
- 查看安装选项：`./install.sh -h`

## 🎯 最佳实践

1. **定期检查**：每周检查一次日志和规则状态
2. **备份配置**：修改配置前备份UFW规则
3. **监控日志**：设置日志监控告警
4. **测试连接**：定期测试EdgeOne API连接
5. **更新维护**：保持脚本为最新版本

---

**需要帮助？** 请查看 [README.md](README.md) 获取完整文档，或运行 `./test.sh` 进行系统诊断。
