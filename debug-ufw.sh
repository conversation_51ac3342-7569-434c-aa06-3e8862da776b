#!/bin/bash

# UFW规则检测调试脚本
# 用于测试和调试UFW规则的检测逻辑

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示UFW状态
show_ufw_status() {
    print_info "当前UFW状态："
    echo "----------------------------------------"
    ufw status numbered
    echo "----------------------------------------"
    echo
}

# 测试端口规则检测
test_port_detection() {
    local test_port="$1"
    
    print_info "测试端口 $test_port 的规则检测："
    
    # 方法1：简单匹配
    print_info "方法1 - 简单匹配 'ALLOW.*$test_port'："
    local rules1=$(ufw status numbered | grep "ALLOW.*$test_port" || true)
    if [[ -n "$rules1" ]]; then
        echo "$rules1"
    else
        echo "  未找到匹配规则"
    fi
    echo
    
    # 方法2：更精确的正则表达式
    print_info "方法2 - 精确匹配 '(ALLOW|DENY).*[[:space:]]${test_port}([[:space:]]|/|,|$)'："
    local rules2=$(ufw status numbered | grep -E "(ALLOW|DENY).*[[:space:]]${test_port}([[:space:]]|/|,|$)" || true)
    if [[ -n "$rules2" ]]; then
        echo "$rules2"
    else
        echo "  未找到匹配规则"
    fi
    echo
    
    # 方法3：包含端口号的任何规则
    print_info "方法3 - 包含端口号的任何规则："
    local rules3=$(ufw status numbered | grep "$test_port" || true)
    if [[ -n "$rules3" ]]; then
        echo "$rules3"
    else
        echo "  未找到匹配规则"
    fi
    echo
}

# 分析UFW输出格式
analyze_ufw_format() {
    print_info "分析UFW输出格式："
    echo
    
    print_info "UFW状态输出的前几行："
    ufw status numbered | head -10 | while IFS= read -r line; do
        echo "  '$line'"
    done
    echo
    
    print_info "包含ALLOW的行："
    ufw status numbered | grep "ALLOW" | head -5 | while IFS= read -r line; do
        echo "  '$line'"
    done
    echo
    
    print_info "规则编号提取测试："
    ufw status numbered | grep "ALLOW" | head -3 | while IFS= read -r line; do
        local rule_num=$(echo "$line" | awk '{print $1}' | tr -d '[]')
        echo "  原始行: '$line'"
        echo "  提取编号: '$rule_num'"
        echo
    done
}

# 创建测试规则
create_test_rules() {
    local test_port="$1"
    
    print_warning "创建测试规则（需要root权限）..."
    
    if [[ $EUID -ne 0 ]]; then
        print_error "需要root权限来创建测试规则"
        return 1
    fi
    
    # 创建几个不同格式的测试规则
    print_info "添加测试规则..."
    ufw allow "$test_port" comment "Test-Rule-1"
    ufw allow from 192.168.1.0/24 to any port "$test_port" comment "Test-Rule-2"
    
    print_success "测试规则已创建"
    show_ufw_status
}

# 清理测试规则
cleanup_test_rules() {
    print_warning "清理测试规则（需要root权限）..."
    
    if [[ $EUID -ne 0 ]]; then
        print_error "需要root权限来清理测试规则"
        return 1
    fi
    
    # 删除包含"Test-Rule"的规则
    local test_rules=$(ufw status numbered | grep "Test-Rule" | awk '{print $1}' | tr -d '[]' | sort -nr)
    
    local removed=0
    for rule_num in $test_rules; do
        if [[ -n "$rule_num" ]]; then
            print_info "删除测试规则 #$rule_num"
            if echo "y" | ufw delete "$rule_num" >/dev/null 2>&1; then
                ((removed++))
            fi
        fi
    done
    
    print_success "清理完成，删除了 $removed 条测试规则"
    show_ufw_status
}

# 主函数
main() {
    local test_port="${1:-44344}"
    
    echo "========================================"
    echo "UFW规则检测调试工具"
    echo "========================================"
    echo
    
    print_info "测试端口: $test_port"
    echo
    
    # 检查UFW是否可用
    if ! command -v ufw &> /dev/null; then
        print_error "UFW未安装"
        exit 1
    fi
    
    # 显示当前状态
    show_ufw_status
    
    # 分析UFW格式
    analyze_ufw_format
    
    # 测试端口检测
    test_port_detection "$test_port"
    
    # 询问是否创建测试规则
    if [[ $EUID -eq 0 ]]; then
        read -p "是否创建测试规则进行测试? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            create_test_rules "$test_port"
            echo
            print_info "重新测试端口检测："
            test_port_detection "$test_port"
            
            read -p "是否清理测试规则? (Y/n): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Nn]$ ]]; then
                cleanup_test_rules
            fi
        fi
    else
        print_warning "以root权限运行可以创建测试规则进行更全面的测试"
    fi
    
    echo
    print_success "调试完成"
}

# 显示帮助
show_help() {
    cat << EOF
UFW规则检测调试工具

用法: $0 [端口号]

参数:
    端口号    要测试的端口号 (默认: 44344)

选项:
    -h, --help    显示此帮助信息

此工具将:
1. 显示当前UFW状态
2. 分析UFW输出格式
3. 测试不同的端口规则检测方法
4. 可选择创建测试规则进行验证

示例:
    $0           # 测试默认端口44344
    $0 80        # 测试端口80
    sudo $0 443  # 以root权限测试端口443（可创建测试规则）

EOF
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    "")
        main
        ;;
    *)
        if [[ "$1" =~ ^[0-9]+$ ]]; then
            main "$1"
        else
            print_error "无效的端口号: $1"
            show_help
            exit 1
        fi
        ;;
esac
